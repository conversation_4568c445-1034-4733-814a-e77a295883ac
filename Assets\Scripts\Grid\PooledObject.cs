using UnityEngine;
using DG.Tweening;

namespace GridSystem
{
    [System.Serializable]
    public class PooledObject : MonoBehaviour
    {
        [HideInInspector] public MeshRenderer meshRenderer;
        [HideInInspector] public MeshFilter meshFilter;
        
        private PlaceableObjectData currentObjectData;
        private Vector2Int gridPosition;
        private Material[] originalMaterials;
        private Material highlightMaterial;
        
        [Header("Animation Settings")]
        private float spawnDuration = 0.5f;
        private float destructionDuration = 0.3f;
        private Ease spawnEase = Ease.OutBack;
        private Ease destructionEase = Ease.InBack;
        
        public void Initialize(MeshRenderer renderer, MeshFilter filter)
        {
            meshRenderer = renderer;
            meshFilter = filter;
            
            // Collider ekle
            if (GetComponent<Collider>() == null)
            {
                gameObject.AddComponent<BoxCollider>();
            }
        }
        
        public void SetObjectData(PlaceableObjectData objectData)
        {
            currentObjectData = objectData;
            
            if (objectData != null && objectData.prefab != null)
            {
                MeshRenderer prefabRenderer = objectData.prefab.GetComponent<MeshRenderer>();
                MeshFilter prefabFilter = objectData.prefab.GetComponent<MeshFilter>();
                highlightMaterial = objectData.highlightMaterial;
                
                if (prefabRenderer != null && prefabFilter != null)
                {
                    if (prefabFilter.sharedMesh != null)
                    {
                        meshFilter.sharedMesh = prefabFilter.sharedMesh;
                    }
                    
                    if (prefabRenderer.sharedMaterials != null && prefabRenderer.sharedMaterials.Length > 0)
                    {
                        originalMaterials = prefabRenderer.sharedMaterials;
                        meshRenderer.sharedMaterials = originalMaterials;
                        meshRenderer.enabled = true;
                        
                        // Spawn animasyonunu başlat
                        PlaySpawnAnimation();
                    }
                    else
                    {
                        Debug.LogWarning($"Prefab '{objectData.prefab.name}' has no materials!");
                    }
                }
                else
                {
                    Debug.LogWarning($"Prefab '{objectData.prefab.name}' doesn't have MeshRenderer or MeshFilter!");
                }
            }
        }
        
        private void PlaySpawnAnimation()
        {
            // Mevcut scale'i kaydet
            Vector3 targetScale = transform.localScale;
            
            // Scale'i sıfırla
            transform.localScale = Vector3.zero;
            
            // Scale animasyonunu başlat
            transform.DOScale(targetScale, spawnDuration)
                .SetEase(spawnEase)
                .OnComplete(() => {
                    // Animasyon tamamlandığında yapılacak işlemler
                });
        }
        
        public void SetGridPosition(Vector2Int position)
        {
            gridPosition = position;
        }
        
        public Vector2Int GetGridPosition()
        {
            return gridPosition;
        }
        
        public PlaceableObjectData GetObjectData()
        {
            return currentObjectData;
        }
        
        public void ClearData()
        {
            transform.DOKill();
            
            currentObjectData = null;
            meshFilter.sharedMesh = null;
            meshRenderer.sharedMaterials = new Material[0];
            meshRenderer.enabled = false;
            gridPosition = Vector2Int.zero;
            originalMaterials = null;
            highlightMaterial = null;
            
            // Scale'i resetle
            transform.localScale = Vector3.one;
        }
        
        private void OnDestroy()
        {
            transform.DOKill();
        }

        public void PlayDestructionAnimation(System.Action onComplete)
        {
            transform.DOScale(Vector3.zero, destructionDuration)
                .SetEase(destructionEase)
                .OnComplete(() => onComplete?.Invoke());
        }

        public void SetHighlight(bool state)
        {
            if (meshRenderer == null || originalMaterials == null || highlightMaterial == null)
                return;

            if (state)
            {
                Material[] highlighted = new Material[originalMaterials.Length];
                for (int i = 0; i < originalMaterials.Length; i++)
                {
                    highlighted[i] = highlightMaterial;
                }
                meshRenderer.sharedMaterials = highlighted;
            }
            else
            {
                meshRenderer.sharedMaterials = originalMaterials;
            }
        }
    }
}
