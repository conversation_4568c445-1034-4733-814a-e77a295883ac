using UnityEngine;
using System.Collections.Generic;

namespace GridSystem
{
    public class GridRenderer : MonoBehaviour
    {
        private Camera mainCamera;
        private Material gridMaterial;
        private List<LineRenderer> gridLines = new List<LineRenderer>();
        
        [Header("Grid Visualization")]
        public Color gridColor = new Color(1f, 1f, 1f, 0.8f);
        public bool showGrid = true;
        public float gridLineWidth = 0.02f;
        public float updateInterval = 0.1f;
        public float gridLineHeight = 0.01f; // Grid çizgilerinin yüksekliği
        
        private float nextUpdateTime;
        private GridManager gridManager;

        private void Awake()
        {
            InitializeComponents();
        }

        private void InitializeComponents()
        {
            if (mainCamera == null) mainCamera = Camera.main;
            if (gridManager == null) gridManager = GetComponent<GridManager>();
            if (gridMaterial == null) CreateGridMaterial();
        }

        private void CreateGridMaterial()
        {
            // Unlit/Color yerine Transparent/Diffuse kullanarak daha iyi görünürlük sağlayalım
            gridMaterial = new Material(Shader.Find("Transparent/Diffuse"));
            gridMaterial.color = gridColor;
        }

        void Update()
        {
            if (!showGrid || mainCamera == null) return;

            if (Time.time >= nextUpdateTime)
            {
                UpdateVisibleGridLines();
                nextUpdateTime = Time.time + updateInterval;
            }
        }

        private void UpdateVisibleGridLines()
        {
            if (gridManager == null)
            {
                InitializeComponents();
                if (gridManager == null) return;
            }

            ClearGridLines();

            Vector3 gridStart = transform.position;
            Vector3 gridEnd = transform.position + new Vector3(gridManager.gridSize.x * gridManager.cellSize, 0, gridManager.gridSize.y * gridManager.cellSize);
            
            // Yatay çizgiler
            for (int z = 0; z <= gridManager.gridSize.y; z++)
            {
                Vector3 lineStart = gridStart + new Vector3(0, gridLineHeight, z * gridManager.cellSize);
                Vector3 lineEnd = new Vector3(gridEnd.x, gridLineHeight, lineStart.z);
                CreateGridLine(lineStart, lineEnd, $"HorizontalLine_{z}");
            }

            // Dikey çizgiler
            for (int x = 0; x <= gridManager.gridSize.x; x++)
            {
                Vector3 lineStart = gridStart + new Vector3(x * gridManager.cellSize, gridLineHeight, 0);
                Vector3 lineEnd = new Vector3(lineStart.x, gridLineHeight, gridEnd.z);
                CreateGridLine(lineStart, lineEnd, $"VerticalLine_{x}");
            }
        }

        private void CreateGridLine(Vector3 start, Vector3 end, string name)
        {
            GameObject lineObj = new GameObject(name);
            lineObj.transform.SetParent(transform);
            
            LineRenderer line = lineObj.AddComponent<LineRenderer>();
            line.material = gridMaterial;
            line.startColor = gridColor;
            line.endColor = gridColor;
            line.startWidth = gridLineWidth;
            line.endWidth = gridLineWidth;
            line.positionCount = 2;
            line.useWorldSpace = true;
            
            // Z-fighting'i önlemek için çizgileri biraz yukarı kaldıralım
            line.SetPosition(0, start);
            line.SetPosition(1, end);

            // Çizgilerin her zaman görünür olması için camera culling'i kapatalım
            line.receiveShadows = false;
            line.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            
            gridLines.Add(line);
        }

        private void ClearGridLines()
        {
            foreach (var line in gridLines)
            {
                if (line != null && line.gameObject != null)
                {
                    DestroyImmediate(line.gameObject);
                }
            }
            gridLines.Clear();
        }

        private void OnDestroy()
        {
            ClearGridLines();
            if (gridMaterial != null)
            {
                DestroyImmediate(gridMaterial);
            }
        }

        void OnDrawGizmos()
        {
            if (!Application.isPlaying && showGrid)
            {
                if (gridManager == null)
                {
                    gridManager = GetComponent<GridManager>();
                    if (gridManager == null) return;
                }
                DrawEditorGrid();
            }
        }

        private void DrawEditorGrid()
        {
            if (gridManager == null) return;

            Gizmos.color = gridColor;
            Vector3 gridOrigin = transform.position;

            // Yatay çizgiler
            for (int z = 0; z <= gridManager.gridSize.y; z++)
            {
                Vector3 startPoint = gridOrigin + new Vector3(0, gridLineHeight, z * gridManager.cellSize);
                Vector3 endPoint = gridOrigin + new Vector3(gridManager.gridSize.x * gridManager.cellSize, gridLineHeight, z * gridManager.cellSize);
                Gizmos.DrawLine(startPoint, endPoint);
            }

            // Dikey çizgiler
            for (int x = 0; x <= gridManager.gridSize.x; x++)
            {
                Vector3 startPoint = gridOrigin + new Vector3(x * gridManager.cellSize, gridLineHeight, 0);
                Vector3 endPoint = gridOrigin + new Vector3(x * gridManager.cellSize, gridLineHeight, gridManager.gridSize.y * gridManager.cellSize);
                Gizmos.DrawLine(startPoint, endPoint);
            }
        }

        void OnValidate()
        {
            InitializeComponents();
            // Inspector'da değerler değiştiğinde grid'i güncelle
            if (Application.isPlaying && gridManager != null)
            {
                UpdateVisibleGridLines();
            }
        }
    }
} 