using UnityEngine;
using System.Collections.Generic;

namespace GridSystem
{
    [RequireComponent(typeof(ObjectPoolingSystem))]
    public class GridPlacementSystem : MonoBehaviour
    {
        private Dictionary<Vector2Int, PlaceableObjectData> placedObjectsData = new Dictionary<Vector2Int, PlaceableObjectData>();
        private PlaceableObjectData selectedObject;
        private GridManager gridManager;
        private ObjectPoolingSystem poolingSystem;

        private void Awake()
        {
            gridManager = GetComponent<GridManager>();
            poolingSystem = GetComponent<ObjectPoolingSystem>();
            
            if (poolingSystem == null)
            {
                Debug.LogError("ObjectPoolingSystem component is missing! Please add it to the GameObject.");
            }
        }

        public void SetSelectedObject(PlaceableObjectData objectData)
        {
            selectedObject = objectData;
        }

        public bool TryPlaceObject(Vector3 worldPosition)
        {
            if (selectedObject == null)
            {
                return false;
            }

            Vector2Int gridPosition = WorldToGridPosition(worldPosition);
            
            Vector2Int adjustedPosition = new Vector2Int(
                gridPosition.x - (selectedObject.size.x - 1) / 2,
                gridPosition.y - (selectedObject.size.y - 1) / 2
            );
            
            if (!IsWithinGridBounds(adjustedPosition, selectedObject.size))
            {
                return false;
            }
            
            if (!IsSpaceAvailable(adjustedPosition, selectedObject.size))
            {
                return false;
            }

            Vector3 basePosition = GridToWorldPosition(adjustedPosition);
            float objectHeight = CalculateObjectHeight(selectedObject);
            
            Vector3 finalPosition = new Vector3(
                basePosition.x,
                transform.position.y + (objectHeight * 0.5f),
                basePosition.z
            );
            
            PooledObject pooledObj = poolingSystem.GetObject(selectedObject, finalPosition, adjustedPosition);
            
            if (pooledObj == null)
            {
                return false;
            }

            // Set the object's current grid reference
            selectedObject.currentGrid = this;

            for (int x = 0; x < selectedObject.size.x; x++)
            {
                for (int y = 0; y < selectedObject.size.y; y++)
                {
                    Vector2Int occupiedPos = adjustedPosition + new Vector2Int(x, y);
                    placedObjectsData[occupiedPos] = selectedObject;
                }
            }

            return true;
        }

        public bool TryRemoveObject(Vector3 worldPosition)
        {
            Vector2Int gridPosition = WorldToGridPosition(worldPosition);
            Debug.Log($"TryRemoveObject - WorldPos: {worldPosition}, GridPos: {gridPosition}, Grid: {name}");

            if (placedObjectsData.TryGetValue(gridPosition, out PlaceableObjectData objectData))
            {
                Debug.Log($"Found object data: {objectData.objectName}");

                Vector2Int adjustedPosition = new Vector2Int(
                    gridPosition.x - (objectData.size.x - 1) / 2,
                    gridPosition.y - (objectData.size.y - 1) / 2
                );

                Debug.Log($"Adjusted position: {adjustedPosition}");

                for (int x = 0; x < objectData.size.x; x++)
                {
                    for (int y = 0; y < objectData.size.y; y++)
                    {
                        Vector2Int occupiedPos = adjustedPosition + new Vector2Int(x, y);
                        placedObjectsData.Remove(occupiedPos);
                    }
                }

                bool poolRemoveResult = poolingSystem.RemoveObjectAtGrid(adjustedPosition);
                Debug.Log($"Pool remove result: {poolRemoveResult}");

                objectData.currentGrid = null; // Clear grid reference when removed
                Debug.Log($"Object removed successfully from grid: {name}");
                return true;
            }
            else
            {
                Debug.LogWarning($"No object found at grid position {gridPosition} in grid {name}");
                // Debug: tüm placed objects'i listele
                Debug.Log($"All placed objects in {name}:");
                foreach (var kvp in placedObjectsData)
                {
                    Debug.Log($"  {kvp.Key}: {kvp.Value.objectName}");
                }
            }

            return false;
        }

        private Vector2Int WorldToGridPosition(Vector3 worldPosition)
        {
            Vector3 localPosition = worldPosition - transform.position;
            
            float x = localPosition.x / gridManager.cellSize;
            float z = localPosition.z / gridManager.cellSize;
            
            return new Vector2Int(
                Mathf.FloorToInt(x),
                Mathf.FloorToInt(z)
            );
        }

        public Vector3 GridToWorldPosition(Vector2Int gridPosition)
        {
            return transform.position + new Vector3(
                (gridPosition.x + 0.5f) * gridManager.cellSize,
                0,
                (gridPosition.y + 0.5f) * gridManager.cellSize
            );
        }

        private bool IsWithinGridBounds(Vector2Int position, Vector2Int size)
        {
            return position.x >= 0 && position.y >= 0 &&
                   position.x + size.x <= gridManager.gridSize.x &&
                   position.y + size.y <= gridManager.gridSize.y;
        }

        private bool IsSpaceAvailable(Vector2Int position, Vector2Int size)
        {
            for (int x = 0; x < size.x; x++)
            {
                for (int y = 0; y < size.y; y++)
                {
                    Vector2Int checkPos = position + new Vector2Int(x, y);
                    if (placedObjectsData.ContainsKey(checkPos))
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private float CalculateObjectHeight(PlaceableObjectData objectData)
        {
            if (objectData == null || objectData.prefab == null) return gridManager.cellSize;
            
            Renderer renderer = objectData.prefab.GetComponent<Renderer>();
            if (renderer != null)
            {
                return renderer.bounds.size.y;
            }
            
            Renderer[] childRenderers = objectData.prefab.GetComponentsInChildren<Renderer>();
            if (childRenderers.Length > 0)
            {
                Bounds bounds = childRenderers[0].bounds;
                for (int i = 1; i < childRenderers.Length; i++)
                {
                    bounds.Encapsulate(childRenderers[i].bounds);
                }
                return bounds.size.y;
            }
            
            return gridManager.cellSize;
        }

        public Vector2Int GetGridPositionFromWorld(Vector3 worldPosition)
        {
            return WorldToGridPosition(worldPosition);
        }

        public PooledObject GetObjectAtCell(Vector2Int cell)
        {
            return poolingSystem?.GetObjectAtCell(cell);
        }

        public bool TryMoveObjectToGrid(Vector3 worldPosition, GridPlacementSystem targetGrid)
        {
            Debug.Log("Taşıma işlemi başladı. Hedef grid: " + targetGrid.name);
            Vector2Int gridPosition = WorldToGridPosition(worldPosition);
            Debug.Log("Grid pozisyonu: " + gridPosition);

            if (!placedObjectsData.TryGetValue(gridPosition, out PlaceableObjectData objectData))
            {
                return false;
            }

            if (objectData.currentGrid == targetGrid)
            {
                return false; // Already on target grid
            }

            // Get the object's current position and size
            PooledObject pooledObj = poolingSystem.GetPooledObjectForData(objectData);
            if (pooledObj == null) return false;
            Vector2Int currentPosition = pooledObj.GetGridPosition();
            Vector2Int size = objectData.size;

            // Remove from current grid
            TryRemoveObject(GridToWorldPosition(currentPosition));

            // Try to place on target grid
            objectData.currentGrid = null; // Clear before placing on new grid
            targetGrid.SetSelectedObject(objectData);
            bool placementSuccess = targetGrid.TryPlaceObject(targetGrid.GridToWorldPosition(currentPosition));

            if (!placementSuccess)
            {
                // If placement failed, try to put it back on original grid
                SetSelectedObject(objectData);
                TryPlaceObject(GridToWorldPosition(currentPosition));
                return false;
            }

            return true;
        }

        public bool TryMoveObjectWithinGrid(Vector2Int fromPosition, Vector2Int toPosition)
        {
            if (!placedObjectsData.TryGetValue(fromPosition, out PlaceableObjectData objectData))
            {
                return false;
            }

            // Hedef pozisyonda nesne var mı kontrol et
            Vector2Int adjustedToPosition = new Vector2Int(
                toPosition.x - (objectData.size.x - 1) / 2,
                toPosition.y - (objectData.size.y - 1) / 2
            );

            // Aynı pozisyonsa hareket etme
            Vector2Int currentAdjustedPosition = new Vector2Int(
                fromPosition.x - (objectData.size.x - 1) / 2,
                fromPosition.y - (objectData.size.y - 1) / 2
            );

            if (adjustedToPosition == currentAdjustedPosition)
            {
                return true; // Already at target position
            }

            // Hedef pozisyon grid sınırları içinde mi?
            if (!IsWithinGridBounds(adjustedToPosition, objectData.size))
            {
                return false;
            }

            // Hedef pozisyonda başka nesne var mı?
            if (!IsSpaceAvailable(adjustedToPosition, objectData.size))
            {
                return false;
            }

            // Mevcut pozisyondan kaldır
            for (int x = 0; x < objectData.size.x; x++)
            {
                for (int y = 0; y < objectData.size.y; y++)
                {
                    Vector2Int occupiedPos = currentAdjustedPosition + new Vector2Int(x, y);
                    placedObjectsData.Remove(occupiedPos);
                }
            }

            // Yeni pozisyona ekle
            for (int x = 0; x < objectData.size.x; x++)
            {
                for (int y = 0; y < objectData.size.y; y++)
                {
                    Vector2Int occupiedPos = adjustedToPosition + new Vector2Int(x, y);
                    placedObjectsData[occupiedPos] = objectData;
                }
            }

            // PooledObject'in grid pozisyonunu güncelle
            PooledObject pooledObj = poolingSystem.GetObjectAtCell(fromPosition);
            if (pooledObj != null)
            {
                pooledObj.SetGridPosition(adjustedToPosition);

                // gridToObject dictionary'sini güncelle
                poolingSystem.UpdateObjectGridPosition(currentAdjustedPosition, adjustedToPosition, pooledObj);
            }

            // currentGrid referansını koru (aynı grid içinde hareket)
            objectData.currentGrid = this;

            return true;
        }
    }
}
