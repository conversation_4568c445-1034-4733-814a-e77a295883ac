using UnityEngine;

namespace GridSystem
{
    [CreateAssetMenu(fileName = "New Placeable Object", menuName = "Level Editor/Placeable Object")]
    public class PlaceableObjectData : ScriptableObject
    {
        public string objectName;
        public GameObject prefab;
        public Vector2Int size = Vector2Int.one; // How many grid cells it occupies (x,y)
        public Sprite icon;
        public Material highlightMaterial; // Used for highlighting during deletion
        [System.NonSerialized] public GridPlacementSystem currentGrid; // Track which grid this object is placed on
    }
}
