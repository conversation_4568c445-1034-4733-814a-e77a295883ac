using UnityEngine;
using GridSystem;

[ExecuteInEditMode]
public class CameraSetup : MonoBehaviour
{
    public GridManager gridManager;
    public float panSpeed = 0.01f;

    private Vector2 lastPanPosition;
    public bool IsPanning { get; private set; }

    private bool blockPanning = false;

    /// <summary>
    /// Sürükleme sırasında kamera kaymasını engelle.
    /// </summary>
    public void BlockPanningDuringDrag(bool block)
    {
        blockPanning = block;
    }

    void Start()
    {
        if (gridManager == null)
        {
            gridManager = FindObjectOfType<GridManager>();
        }
        CenterCameraOnGrid();
    }

    void OnValidate()
    {
        if (gridManager != null)
        {
            CenterCameraOnGrid();
        }
    }

    void Update()
    {
        if (Application.isPlaying)
        {
            HandlePanning();
        }
        else
        {
            CenterCameraOnGrid();
        }
    }

    void HandlePanning()
    {
        if (blockPanning) return;
        // Mouse veya dokunmatik girişi al
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            
            if (touch.phase == TouchPhase.Began)
            {
                IsPanning = true;
                lastPanPosition = touch.position;
            }
            else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
            {
                IsPanning = false;
            }
            else if (touch.phase == TouchPhase.Moved && IsPanning)
            {
                PanCamera(touch.position);
            }
        }
        else // Mouse kontrolü
        {
            if (Input.GetMouseButtonDown(0))
            {
                IsPanning = true;
                lastPanPosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                IsPanning = false;
            }
            else if (Input.GetMouseButton(0) && IsPanning)
            {
                PanCamera(Input.mousePosition);
            }
        }
    }

    void PanCamera(Vector2 newPosition)
    {
        Vector2 delta = (Vector2)newPosition - lastPanPosition;

        // Kamera hareketini hesapla
        Vector3 movement = new Vector3(
            -delta.x * panSpeed,
            0,
            -delta.y * panSpeed
        );

        // Kamera rotasyonuna göre hareketi düzelt
        movement = Quaternion.Euler(0, transform.eulerAngles.y, 0) * movement;
        
        // Kamerayı hareket ettir
        transform.position += movement;
        
        lastPanPosition = newPosition;
    }

    void CenterCameraOnGrid()
    {
        if (gridManager == null) return;

        // Grid'in merkez noktasını hesapla
        Vector3 gridCenter = gridManager.transform.position + new Vector3(
            gridManager.gridSize.x * gridManager.cellSize * 0.5f,
            0f,
            gridManager.gridSize.y * gridManager.cellSize * 0.5f
        );

        // Kamerayı grid merkezinin tam üzerine konumlandır
        // Yüksekliği grid'i rahatça görebilmek için ayarla
        float cameraHeight = Mathf.Max(gridManager.gridSize.x, gridManager.gridSize.y) * gridManager.cellSize * 0.8f;
        
        // Kamerayı sadece pozisyon olarak merkeze taşı
        transform.position = new Vector3(
            gridCenter.x,
            cameraHeight,
            gridCenter.z
        );
    }
}