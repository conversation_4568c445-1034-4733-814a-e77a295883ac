using UnityEngine;
using UnityEditor;

namespace GridSystem
{
    [RequireComponent(typeof(Grid<PERSON>enderer))]
    [RequireComponent(typeof(GridPlacementSystem))]
    public class GridManager : MonoBehaviour
    {
        [Header("Grid Settings")]
        public float cellSize = 1f;
        public Vector2Int gridSize = new Vector2Int(10, 10);
        public bool showDebugLines = false;
        
        private const string GROUND_LAYER_NAME = "Ground";
        private const int GROUND_LAYER_INDEX = 7;
        
        private BoxCollider gridCollider;
        private GridRenderer gridRenderer;
        private GridPlacementSystem placementSystem;

        void Awake()
        {
            SetupComponents();
            SetupGridCollider();
            SetupLayerAndInputHandler();
        }

        private void SetupComponents()
        {
            gridRenderer = GetComponent<GridRenderer>();
            placementSystem = GetComponent<GridPlacementSystem>();
        }

        private void SetupGridCollider()
        {
            gridCollider = gameObject.GetComponent<BoxCollider>();
            if (gridCollider == null)
            {
                gridCollider = gameObject.AddComponent<BoxCollider>();
            }
            UpdateGridCollider();
        }

        private void UpdateGridCollider()
        {
            if (gridCollider == null) return;

            float width = gridSize.x * cellSize;
            float length = gridSize.y * cellSize;
            
            gridCollider.size = new Vector3(width, 0.1f, length);
            gridCollider.center = new Vector3(width / 2f, 0f, length / 2f);
            
            gameObject.layer = GROUND_LAYER_INDEX;
        }

        private void SetupLayerAndInputHandler()
        {
    #if UNITY_EDITOR
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty layers = tagManager.FindProperty("layers");

            SerializedProperty layerSP = layers.GetArrayElementAtIndex(GROUND_LAYER_INDEX);
            if (string.IsNullOrEmpty(layerSP.stringValue))
            {
                layerSP.stringValue = GROUND_LAYER_NAME;
                tagManager.ApplyModifiedProperties();
            }
    #endif

            var inputHandler = FindObjectOfType<PlacementInputHandler>();
            if (inputHandler != null)
            {
                int targetMask = 1 << GROUND_LAYER_INDEX;
                if (inputHandler.groundLayer != targetMask)
                {
                    inputHandler.groundLayer = targetMask;
                }
            }
            else
            {
                Debug.LogError("PlacementInputHandler bulunamadı!");
            }
        }

        void OnValidate()
        {
            if (Application.isPlaying)
            {
                UpdateGridCollider();
            }
        }

        public void SetSelectedObject(PlaceableObjectData objectData)
        {
            if (placementSystem != null)
            {
                placementSystem.SetSelectedObject(objectData);
            }
        }

        public bool TryPlaceObject(Vector3 worldPosition)
        {
            if (placementSystem != null)
            {
                return placementSystem.TryPlaceObject(worldPosition);
            }
            return false;
        }

        public bool TryRemoveObject(Vector3 worldPosition)
        {
            if (placementSystem != null)
            {
                return placementSystem.TryRemoveObject(worldPosition);
            }
            return false;
        }
    }
}