using UnityEngine;
using GridSystem;
using UnityEngine.EventSystems;

public class PlacementInputHandler : MonoBehaviour
{
    public GridPlacementSystem placementSystem;
    public Camera mainCamera;
    public LayerMask groundLayer;
    public ObjectPlacementUI placementUI;
    
    private CameraSetup cameraSetup;
    private bool isDragging = false;
    private Vector2 dragStartPosition;
    private const float dragThreshold = 5f;
    private EventSystem eventSystem;
    private PooledObject currentHoverObject;
    private PooledObject draggedObject;
    private GridPlacementSystem originalGrid;
    private Vector3 dragOffset;
    private Vector2Int originalGridPosition;

    private void Start()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }
        
        cameraSetup = mainCamera.GetComponent<CameraSetup>();
        if (cameraSetup == null)
        {
            Debug.LogError("CameraSetup component is missing on the main camera!");
        }

        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            Debug.LogError("EventSystem not found in the scene!");
        }
    }

    private void Update()
    {
        if (IsPointerOverUI())
        {
            isDragging = false;
            ClearHover();
            return;
        }

        HandleHover();

        if (Input.GetMouseButtonDown(0))
        {
            isDragging = true;
            dragStartPosition = Input.mousePosition;

            // Grid üzerinden nesne seçimi
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                // Hangi grid sistemine tıklandığını bul
                GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                if (hitGrid != null)
                {
                    Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                    draggedObject = hitGrid.GetObjectAtCell(gridPos);
                    if (draggedObject != null)
                    {
                        originalGrid = hitGrid;
                        originalGridPosition = draggedObject.GetGridPosition();
                        dragOffset = draggedObject.transform.position - hit.point;
                        Debug.Log("Nesne seçildi: " + draggedObject.name + " Grid: " + originalGrid.name);
                    }
                }
            }
        }
        else if (Input.GetMouseButton(0) && draggedObject != null)
        {
            // Update object position while dragging
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                // Hangi grid sistemine sürüklendiğini bul
                GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                if (hitGrid != null)
                {
                    // Grid hücresine snap yap
                    Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                    Vector3 snappedPosition = hitGrid.GridToWorldPosition(gridPos);
                    // Y eksenini koru
                    snappedPosition.y = draggedObject.transform.position.y;
                    draggedObject.transform.position = snappedPosition;
                }

                // Camera pan'ı engellemek için flag set et
                if (cameraSetup != null)
                {
                    cameraSetup.BlockPanningDuringDrag(true);
                }
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            if (!isDragging) return;

            // Sürükleme bittiğinde pan engelini kaldır
            if (cameraSetup != null)
            {
                cameraSetup.BlockPanningDuringDrag(false);
            }

            if (draggedObject != null)
            {
                bool placementSuccessful = false;

                // Try to place on new grid
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                {
                    // Find the target grid system
                    GridPlacementSystem targetGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    if (targetGrid != null)
                    {
                        Vector2Int targetGridPos = targetGrid.GetGridPositionFromWorld(hit.point);
                        float originalY = draggedObject.transform.position.y;

                        if (targetGrid != originalGrid)
                        {
                            // Farklı bir gride taşıma
                            if (targetGrid.GetObjectAtCell(targetGridPos) == null)
                            {
                                // Nesnenin object data'sını al
                                PlaceableObjectData objectData = draggedObject.GetObjectData();

                                // Orijinal griddeki nesneyi sil
                                originalGrid.TryRemoveObject(draggedObject.transform.position);

                                // Hedef gride yerleştir
                                targetGrid.SetSelectedObject(objectData);
                                Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                                newPos.y = originalY;

                                if (targetGrid.TryPlaceObject(newPos))
                                {
                                    placementSuccessful = true;
                                    Debug.Log("Nesne başarıyla taşındı: " + draggedObject.name);
                                }
                            }
                        }
                        else
                        {
                            // Aynı grid içinde hareket
                            if (targetGrid.GetObjectAtCell(targetGridPos) == null || targetGridPos == originalGridPosition)
                            {
                                Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                                newPos.y = originalY;
                                draggedObject.transform.position = newPos;
                                placementSuccessful = true;
                            }
                        }
                    }
                }

                // Eğer yerleştirme başarısız olduysa orijinal pozisyona geri koy
                if (!placementSuccessful)
                {
                    Vector3 originalPos = originalGrid.GridToWorldPosition(originalGridPosition);
                    originalPos.y = draggedObject.transform.position.y;
                    draggedObject.transform.position = originalPos;
                    Debug.Log("Nesne orijinal pozisyonuna geri döndürüldü");
                }

                draggedObject = null;
                originalGrid = null;
            }
            else
            {
                // Original click handling
                float dragDistance = Vector2.Distance(dragStartPosition, Input.mousePosition);
                if (dragDistance < dragThreshold && !cameraSetup.IsPanning)
                {
                    if (placementUI != null && placementUI.IsDeleteModeActive())
                    {
                        TryDeleteObject();
                    }
                    else
                    {
                        TryPlaceObject();
                    }
                }
            }

            isDragging = false;
        }
    }

    private bool IsPointerOverUI()
    {
        // Touch kontrolü
        if (Input.touchCount > 0)
        {
            return EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId);
        }
        
        // Mouse kontrolü
        return EventSystem.current.IsPointerOverGameObject();
    }

    private void TryPlaceObject()
    {
        Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
        {
            placementSystem.TryPlaceObject(hit.point);
        }
    }

    private void HandleHover()
    {
        if (placementUI != null && placementUI.IsDeleteModeActive())
        {
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                Vector2Int gridPos = placementSystem.GetGridPositionFromWorld(hit.point);
                PooledObject hoverObj = placementSystem.GetObjectAtCell(gridPos);
                
                if (hoverObj != currentHoverObject)
                {
                    ClearHover();
                    currentHoverObject = hoverObj;
                    if (currentHoverObject != null)
                    {
                        currentHoverObject.SetHighlight(true);
                    }
                }
            }
            else
            {
                ClearHover();
            }
        }
    }

    private void ClearHover()
    {
        if (currentHoverObject != null)
        {
            currentHoverObject.SetHighlight(false);
            currentHoverObject = null;
        }
    }

    private void TryDeleteObject()
    {
        if (currentHoverObject != null)
        {
            Vector3 worldPos = currentHoverObject.transform.position;
            placementSystem.TryRemoveObject(worldPos);
            ClearHover();
        }
    }
}
