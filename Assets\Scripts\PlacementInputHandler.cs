using UnityEngine;
using GridSystem;
using UnityEngine.EventSystems;

public class PlacementInputHandler : MonoBehaviour
{
    public GridPlacementSystem placementSystem;
    public Camera mainCamera;
    public LayerMask groundLayer;
    public ObjectPlacementUI placementUI;
    
    private CameraSetup cameraSetup;
    private bool isDragging = false;
    private Vector2 dragStartPosition;
    private const float dragThreshold = 5f;
    private EventSystem eventSystem;
    private PooledObject currentHoverObject;
    private PooledObject draggedObject;
    private GridPlacementSystem originalGrid;
    private Vector3 dragOffset;

    private void Start()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }
        
        cameraSetup = mainCamera.GetComponent<CameraSetup>();
        if (cameraSetup == null)
        {
            Debug.LogError("CameraSetup component is missing on the main camera!");
        }

        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            Debug.LogError("EventSystem not found in the scene!");
        }
    }

    private void Update()
    {
        if (IsPointerOverUI())
        {
            isDragging = false;
            ClearHover();
            return;
        }

        HandleHover();

        if (Input.GetMouseButtonDown(0))
        {
            isDragging = true;
            dragStartPosition = Input.mousePosition;
            
            // Grid üzerinden nesne seçimi
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, Mathf.Infinity, groundLayer))
            {
                Vector2Int gridPos = placementSystem.GetGridPositionFromWorld(hit.point);
                draggedObject = placementSystem.GetObjectAtCell(gridPos);
                if (draggedObject != null)
                {
                    originalGrid = placementSystem;
                    dragOffset = draggedObject.transform.position - hit.point;
                    Debug.Log("Nesne seçildi: " + draggedObject.name);
                }
            }
        }
        else if (Input.GetMouseButton(0) && draggedObject != null)
        {
            // Update object position while dragging
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, Mathf.Infinity, groundLayer))
            {
                // Grid hücresine snap yap
                Vector2Int gridPos = placementSystem.GetGridPositionFromWorld(hit.point);
                Vector3 snappedPosition = placementSystem.GridToWorldPosition(gridPos);
                // Y eksenini koru
                snappedPosition.y = draggedObject.transform.position.y;
                draggedObject.transform.position = snappedPosition;

                // Camera pan'ı engellemek için flag set et
                if (cameraSetup != null)
                {
                    cameraSetup.BlockPanningDuringDrag(true);
                }
                Debug.Log("Nesne sürükleniyor: " + draggedObject.name);
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            if (!isDragging) return;

            // Sürükleme bittiğinde pan engelini kaldır
            if (cameraSetup != null)
            {
                cameraSetup.BlockPanningDuringDrag(false);
            }

            if (draggedObject != null)
            {
                // Try to place on new grid
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                RaycastHit hit;
                if (Physics.Raycast(ray, out hit, Mathf.Infinity, groundLayer))
                {
                    // Find the target grid system
                    GridPlacementSystem targetGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    Vector2Int targetGridPos = placementSystem.GetGridPositionFromWorld(hit.point);
                    float originalY = draggedObject.transform.position.y;
                    if (targetGrid != null && targetGrid != originalGrid)
                    {
                        // Hedef gridde hücre doluysa taşıma yapma
                        if (targetGrid.GetObjectAtCell(targetGridPos) == null)
                        {
                            // Orijinal griddeki nesneyi sil
                            originalGrid.TryRemoveObject(draggedObject.transform.position);
                            // Hedef gride ekle
                            Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                            newPos.y = originalY;
                            draggedObject.transform.position = newPos;
                            targetGrid.TryPlaceObject(newPos);
                        }
                        else
                        {
                            // Hedef hücre doluysa eski yerine geri koy
                            Vector2Int gridPos = originalGrid.GetGridPositionFromWorld(draggedObject.transform.position);
                            Vector3 oldPos = originalGrid.GridToWorldPosition(gridPos);
                            oldPos.y = originalY;
                            draggedObject.transform.position = oldPos;
                        }
                    }
                    else
                    {
                        // Return to original position if not dropped on a valid grid
                        Vector2Int gridPos = originalGrid.GetGridPositionFromWorld(draggedObject.transform.position);
                        Vector3 oldPos = originalGrid.GridToWorldPosition(gridPos);
                        oldPos.y = originalY;
                        draggedObject.transform.position = oldPos;
                    }
                }

                draggedObject = null;
                originalGrid = null;
            }
            else
            {
                // Original click handling
                float dragDistance = Vector2.Distance(dragStartPosition, Input.mousePosition);
                if (dragDistance < dragThreshold && !cameraSetup.IsPanning)
                {
                    if (placementUI != null && placementUI.IsDeleteModeActive())
                    {
                        TryDeleteObject();
                    }
                    else
                    {
                        TryPlaceObject();
                    }
                }
            }

            isDragging = false;
        }
    }

    private bool IsPointerOverUI()
    {
        // Touch kontrolü
        if (Input.touchCount > 0)
        {
            return EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId);
        }
        
        // Mouse kontrolü
        return EventSystem.current.IsPointerOverGameObject();
    }

    private void TryPlaceObject()
    {
        Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;

        if (Physics.Raycast(ray, out hit, Mathf.Infinity, groundLayer))
        {
            placementSystem.TryPlaceObject(hit.point);
        }
    }

    private void HandleHover()
    {
        if (placementUI != null && placementUI.IsDeleteModeActive())
        {
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit, Mathf.Infinity, groundLayer))
            {
            Vector2Int gridPos = placementSystem.GetGridPositionFromWorld(hit.point);
            PooledObject hoverObj = placementSystem.GetObjectAtCell(gridPos);
                
                if (hoverObj != currentHoverObject)
                {
                    ClearHover();
                    currentHoverObject = hoverObj;
                    if (currentHoverObject != null)
                    {
                        currentHoverObject.SetHighlight(true);
                    }
                }
            }
            else
            {
                ClearHover();
            }
        }
    }

    private void ClearHover()
    {
        if (currentHoverObject != null)
        {
            currentHoverObject.SetHighlight(false);
            currentHoverObject = null;
        }
    }

    private void TryDeleteObject()
    {
        if (currentHoverObject != null)
        {
            Vector3 worldPos = currentHoverObject.transform.position;
            placementSystem.TryRemoveObject(worldPos);
            ClearHover();
        }
    }
}
