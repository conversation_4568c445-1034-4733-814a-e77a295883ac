using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections.Generic;
using System.IO;
using UnityEditor.SceneManagement;


public class ExtractUsedAssetsFromFolder : EditorWindow
{
    [SerializeField] private DefaultAsset packageFolder;
    [SerializeField] private DefaultAsset outputFolder;

    private Dictionary<string, string> copiedAssetMap = new(); // Eski -> Yeni path

    [MenuItem("Tools/Asset Cleaner")]
    public static void ShowWindow()
    {
        GetWindow<ExtractUsedAssetsFromFolder>("Asset Cleaner From Package");
    }

    void OnGUI()
    {
        GUILayout.Label("🎯 Sahnede Kullanılan Paket Assetlerini Ayıkla", EditorStyles.boldLabel);
        GUILayout.Space(10);

        packageFolder = (DefaultAsset)EditorGUILayout.ObjectField("📦 Paket Klasörü", packageFolder, typeof(DefaultAsset), false);
        outputFolder  = (DefaultAsset)EditorGUILayout.ObjectField("📁 Temiz Çıkış Klasörü", outputFolder, typeof(DefaultAsset), false);

        GUILayout.Space(15);
        if (GUILayout.Button("🚀 Kullanılanları Kopyala ve Referansları Güncelle"))
        {
            if (packageFolder == null || outputFolder == null)
            {
                EditorUtility.DisplayDialog("Eksik Seçim", "Lütfen hem Paket klasörünü hem Çıkış klasörünü seçin.", "Tamam");
                return;
            }

            string packagePath = AssetDatabase.GetAssetPath(packageFolder);
            string outputPath  = AssetDatabase.GetAssetPath(outputFolder);

            CopyAndRedirectAssets(packagePath, outputPath);
        }
    }

    void CopyAndRedirectAssets(string packagePath, string outputPath)
    {
        copiedAssetMap.Clear();

        var usedAssets = new HashSet<string>();
        var allRoots = SceneManager.GetActiveScene().GetRootGameObjects();

        foreach (var root in allRoots)
        {
            foreach (var obj in root.GetComponentsInChildren<Transform>(true))
            {
                var go = obj.gameObject;

                // 1. Prefab
                var prefab = PrefabUtility.GetCorrespondingObjectFromSource(go);
                if (prefab != null) TryAddAsset(prefab, packagePath, usedAssets);

                // 2. Renderer
                var renderer = go.GetComponent<Renderer>();
                if (renderer != null)
                {
                    foreach (var mat in renderer.sharedMaterials)
                    {
                        if (mat == null) continue;
                        TryAddAsset(mat, packagePath, usedAssets);
                        foreach (var texName in mat.GetTexturePropertyNames())
                        {
                            var tex = mat.GetTexture(texName);
                            if (tex != null) TryAddAsset(tex, packagePath, usedAssets);
                        }
                    }
                }

                // 3. UI Text
                var text = go.GetComponent<Text>();
                if (text != null && text.font != null)
                {
                    TryAddAsset(text.font, packagePath, usedAssets);
                }

                // 4. UI Image
                var image = go.GetComponent<Image>();
                if (image != null && image.sprite != null)
                {
                    TryAddAsset(image.sprite, packagePath, usedAssets);
                }

                // 5. TextMeshPro
                var tmp = go.GetComponent<TextMeshProUGUI>();
                if (tmp != null)
                {
                    if (tmp.font != null)
                        TryAddAsset(tmp.font, packagePath, usedAssets);

                    if (tmp.spriteAsset != null)
                        TryAddAsset(tmp.spriteAsset, packagePath, usedAssets);
                }
            }
        }

        // Kopyalama ve mapping
        int copyCount = 0;
        foreach (var sourcePath in usedAssets)
        {
            string relativePath = sourcePath.Substring(packagePath.Length).TrimStart('/');
            string destPath = Path.Combine(outputPath, relativePath).Replace("\\", "/");

            if (File.Exists(destPath)) {
                copiedAssetMap[sourcePath] = destPath;
                continue;
            }

            string destDir = Path.GetDirectoryName(destPath);
            if (!Directory.Exists(destDir)) Directory.CreateDirectory(destDir);

            if (AssetDatabase.CopyAsset(sourcePath, destPath))
            {
                copiedAssetMap[sourcePath] = destPath;
                copyCount++;
            }
        }

        AssetDatabase.Refresh();

        // Referansları yeni assetlerle güncelle
        foreach (var root in allRoots)
        {
            foreach (var obj in root.GetComponentsInChildren<Transform>(true))
            {
                var go = obj.gameObject;

                // Renderer
                var renderer = go.GetComponent<Renderer>();
                if (renderer != null)
                {
                    var mats = renderer.sharedMaterials;
                    for (int i = 0; i < mats.Length; i++)
                        renderer.sharedMaterials[i] = GetNewAsset<Material>(mats[i]);
                }

                // Text
                var text = go.GetComponent<Text>();
                if (text != null)
                    text.font = GetNewAsset<Font>(text.font);

                // Image
                var image = go.GetComponent<Image>();
                if (image != null)
                    image.sprite = GetNewAsset<Sprite>(image.sprite);

                // TextMeshPro
                var tmp = go.GetComponent<TextMeshProUGUI>();
                if (tmp != null)
                {
                    tmp.font = GetNewAsset<TMP_FontAsset>(tmp.font);
                    tmp.spriteAsset = GetNewAsset<TMP_SpriteAsset>(tmp.spriteAsset);
                }
            }
        }

        EditorSceneManager.MarkSceneDirty(SceneManager.GetActiveScene());
        EditorUtility.DisplayDialog("Tamamlandı", $"{copyCount} asset kopyalandı ve referanslar güncellendi.", "Harika");
    }

    void TryAddAsset(Object obj, string packagePath, HashSet<string> set)
    {
        string path = AssetDatabase.GetAssetPath(obj);
        if (!string.IsNullOrEmpty(path) && path.StartsWith(packagePath))
            set.Add(path);
    }

    T GetNewAsset<T>(Object original) where T : Object
    {
        if (original == null) return null;
        string oldPath = AssetDatabase.GetAssetPath(original);
        if (copiedAssetMap.TryGetValue(oldPath, out string newPath))
            return AssetDatabase.LoadAssetAtPath<T>(newPath);
        return original as T;
    }
}
